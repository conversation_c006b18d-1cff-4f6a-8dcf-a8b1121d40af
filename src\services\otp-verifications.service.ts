import bcrypt from "bcrypt";
import { eq } from "drizzle-orm";
import { FastifyReply } from "fastify";

import db from "@/db";
import { otp, users } from "@/db/schemas";
import { CustomErrorType } from "@/types";

const services = {
  generateUniqueOtp: async () => {
    try {
      let otpCode;
      let isUnique = false;

      while (!isUnique) {
        // Generate OTP code
        otpCode = Math.floor(100000 + Math.random() * 900000).toString();

        // Check if the OTP exists in the database
        const findOtp = await db()
          .select()
          .from(otp)
          .where(eq(otp.code, otpCode));

        if (!findOtp || findOtp.length === 0) {
          isUnique = true; // OTP is unique, exit the loop
        }
      }

      return otpCode;
    } catch (err) {
      console.error(`Unexpected error: ${err}`);
      throw err;
    }
  },

  otpGenerate: async (data: { username?: string }) => {
    try {
      // generate otp code 6 code
      const otpCode = await services.generateUniqueOtp();

      const findUser = await db()
        .select()
        .from(users)
        .where(eq(users.username, data.username || ""));
      if (!findUser) {
        const error: CustomErrorType = new Error("User not found");
        error.statusCode = 404;
        throw error;
      }

      // save otp code to database
      const otpCreated = await db()
        .insert(otp)
        .values({
          code: otpCode,
          user_id: findUser[0].id,
          expired_at: new Date(Date.now() + 1000 * 60 * 5), // 5 minutes
        });
      if (!otpCreated) {
        const error: CustomErrorType = new Error("Failed to Generate OTP");
        error.statusCode = 400;
        throw error;
      }

      console.log("OTP GENERATED SUCCESSFULLY: " + otpCode);
      console.log("RESULT CMS GENERATE OTP" + JSON.stringify(otpCreated));

      return {
        success: true,
        message: "OTP generated successfully",
        otpCode: otpCode,
      };
    } catch (err) {
      console.error(`Unexpected error: ${err}`);
      throw err;
    }
  },

  otpVerification: async ({
    code,
    type,
    username,
    password,
    reply,
  }: {
    code: number;
    type: "register" | "login" | "forgotPassword";
    username: string;
    password?: string;
    reply: FastifyReply;
  }) => {
    try {
      // find code otp in database
      const findOtp = await db()
        .select()
        .from(otp)
        .where(eq(otp.code, code.toString()));

      if (!findOtp) {
        const error: CustomErrorType = new Error("OTP not found");
        error.statusCode = 404;
        throw error;
      }

      // check expired otp
      if (findOtp[0].expired_at < new Date()) {
        const error: CustomErrorType = new Error("OTP expired");
        error.statusCode = 400;
        throw error;
      }

      // delete otp code in database
      await db().delete(otp).where(eq(otp.code, code.toString()));

      // get user id from database
      const findUser = await db()
        .select()
        .from(users)
        .where(eq(users.username, username));

      if (!findUser) {
        const error: CustomErrorType = new Error("User not found");
        error.statusCode = 404;
        throw error;
      }
      if (
        (type === "register" || type === "forgotPassword") &&
        password &&
        password.length > 0
      ) {
        const hashedPassword = await bcrypt.hash(password, 10);

        await db()
          .update(users)
          .set({
            password: hashedPassword,
          })
          .where(eq(users.id, findUser[0].id));
      }

      if (type === "forgotPassword") {
        return null;
      }

      const userId = findUser[0].id;
      const userUsername = findUser[0].username;

      const token = await reply.jwtSign(
        {
          userId: userId,
          username: userUsername,
        },
        { expiresIn: "12h" }
      );
      const refreshToken = await reply.jwtSign(
        { userId: userId },
        { expiresIn: "30d" }
      );

      const finalResult = {
        token,
        refreshToken,
        user: findUser[0],
        duration: 43200, // 12 hours,
      };

      console.log("RESULT CMS VERIFY OTP" + JSON.stringify(finalResult));

      return finalResult;
    } catch (err) {
      console.error(`Unexpected error: ${err}`);
      throw err;
    }
  },
};

export default services;
