import bcrypt from "bcrypt";
import { eq } from "drizzle-orm";
import { FastifyReply, FastifyRequest } from "fastify";

import db from "@/db";
import { users } from "@/db/schemas";
import { CustomErrorType } from "@/types";

const register = async (data: any) => {
  try {
    const {
      username = "user",
      password = "password123",
      point = 0,
    } = data;

    const hashedPassword = await bcrypt.hash(password, 10);

    // insert into db
    const createdUser = await db().insert(users).values({
      username: username,
      password: hashedPassword,
      point: point,
      latest_usage_bonus_level: null,
    });

    if (!createdUser) {
      const error: CustomErrorType = new Error("Failed to create user");
      error.statusCode = 404;
      throw error;
    }

    console.log("RESULT BODY REGISTER " + JSON.stringify(data));

    return createdUser;
  } catch (err) {
    console.error(`Unexpected error: ${err}`);
    throw err;
  }
};

const login = async (username: string, password: string, reply: FastifyReply) => {
  try {
    // check user
    const findUser = await db()
      .select()
      .from(users)
      .where(eq(users.username, username));

    if (!findUser || findUser.length === 0) {
      const error: CustomErrorType = new Error("User not found");
      error.statusCode = 404;
      throw error;
    }

    const hashedPassword = findUser[0].password || "";
    const isPasswordMatch = await bcrypt.compare(password, hashedPassword);

    if (!isPasswordMatch) {
      const error: CustomErrorType = new Error("Password is incorrect");
      error.statusCode = 400;
      throw error;
    }
    const userId = findUser[0].id;
    const userUsername = findUser[0].username;

    const token = await reply.jwtSign(
      {
        userId: userId,
        username: userUsername,
      },
      { expiresIn: "12h" }
    );
    const refreshToken = await reply.jwtSign(
      { userId: userId },
      { expiresIn: "30d" }
    );

    return {
      token,
      refreshToken,
      userData: findUser[0],
      duration: 43200, // 12 hours,
    };
  } catch (err) {
    console.error(`Unexpected error: ${err}`);
    throw err;
  }
};

// TODO RESET LEVEL HISTORY EVERYDAY
// TODO RESET BONUS POINT IF LATEST 7 DAYS AGO
const resetUserScheduler = async (data: any) => {
  try {
    // TODO RESET LEVEL HISTORY EVERYDAY
    // TODO RESET BONUS POINT IF LATEST 7 DAYS AGO
  } catch (err) {
    console.error(`Unexpected error: ${err}`);
    throw err;
  }
};

// TODO MAKED HISTORY LEVEL OR USER EXPORT
const exportUser = async (data: any) => {
  try {
    // TODO MAKED HISTORY LEVEL OR USER EXPORT
  } catch (err) {
    console.error(`Unexpected error: ${err}`);
    throw err;
  }
};

export default {
  register,
  login,
  resetUserScheduler,
  exportUser,
}; 