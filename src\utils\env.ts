import { config } from "dotenv";

config();

const env = {
  APP_MODE: process.env.APP_MODE || "development",
  APP_JWT_SECRET: process.env.APP_JWT_SECRET,
  HOST: process.env.HOST || "localhost",
  PORT: process.env.PORT || 3000,
  DATABASE_URL:
    process.env.DATABASE_URL ||
    "postgres://postgres:postgres@localhost:5432/postgres",
  DATABASE_SSL: process.env.DATABASE_SSL === "false" ? false : true,

  DB: {
    HOST: process.env.DB_HOST,
    USER: process.env.DB_USER,
    PASSWORD: process.env.DB_PASSWORD,
    DATABASE: process.env.DB_DATABASE,
    PORT: process.env.DB_PORT || 5432,
    SSL: process.env.DB_SSL === "false" ? false : true,
    URL: process.env.DB_URL,

    HOST_EXTERNAL: process.env.DB_HOST_EXTERNAL,
    USER_EXTERNAL: process.env.DB_USER_EXTERNAL,
    PASSWORD_EXTERNAL: process.env.DB_PASSWORD_EXTERNAL,
    DATABASE_EXTERNAL: process.env.DB_DATABASE_EXTERNAL,
    PORT_EXTERNAL: process.env.DB_PORT_EXTERNAL || 5432,
    SSL_EXTERNAL: process.env.DB_SSL_EXTERNAL === "false" ? false : true,
    URL_EXTERNAL: process.env.DB_URL_EXTERNAL,
  },

  API_LIMIT_API_CALL: process.env.API_LIMIT_API_CALL || 54000,
};

// Log the extracted environment variables
console.log(env);

export default env;
