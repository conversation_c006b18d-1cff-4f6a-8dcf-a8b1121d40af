import { CustomErrorType } from "@/types";

export const submitForm = async (data: any) => {
  try {
    const {
      template_type,
      image_url,
      generating_image,
      tnc = "true",
      overallOptInStatus,
      typeOfSignup,
      cid,
      utmCampaign,
      utmContent,
      utmMedium,
      utmSource,
      utmTerm,
      hubspotutk,
    } = data;

    // Process form data without PII
    const formData = {
      template_type: template_type || "default",
      image_url: image_url || "",
      generating_image: generating_image || "",
      tnc: tnc || "true",
      overallOptInStatus: overallOptInStatus || "false",
      typeOfSignup: typeOfSignup || "standard",
      cid: cid || "anonymous",
      utmCampaign: utmCampaign || "default",
      utmContent: utmContent || "default",
      utmMedium: utmMedium || "default",
      utmSource: utmSource || "default",
      utmTerm: utmTerm || "default",
    };

    console.log("FORM SUBMITTED SUCCESSFULLY: " + JSON.stringify(formData));

    return {
      success: true,
      message: "Form submitted successfully",
      data: formData,
    };
  } catch (err) {
    console.log(err);
    throw err;
  }
};

export default {
  submitForm,
}; 