import { FastifyInstance } from "fastify";

import { authValidation } from "@/middlewares/auth.middleware";

import controllers from "../controllers/forms.controller";
// import { $ref } from "../schemas";
// import { fastifyPassport } from "../";

async function formsRouter(fastify: FastifyInstance) {
  fastify.route({
    method: "POST",
    url: "/submit",
    // schema: {
    //   body: $ref("otpVerificationsRegisterSchema"),
    // },
    onRequest: [authValidation],
    handler: controllers.formsSubmit,
  });
}

export default formsRouter;
