import { FastifyReply, FastifyRequest } from "fastify";

import { customError, customResponse } from "@/helpers";
import { UsersLoginSchemaType, UsersRegisterSchemasType } from "@/schemas/users.schema";
import services from "@/services/otp-verifications.service";

const controllers = {
  otpGenerate: async (
    req: FastifyRequest<{
      Body: UsersLoginSchemaType;
    }>,
    reply: FastifyReply
  ) => {
    try {
      const { username } = req.body;

      await services.otpGenerate({
        username: username,
      });

      return customResponse(reply, null, 200, "OTP Generated Success");
    } catch (error) {
      return customError(error, reply);
    }
  },
  otpVerification: async (
    req: FastifyRequest<{
      Body: UsersRegisterSchemasType;
    }>,
    reply: FastifyReply
  ) => {
    try {
      const { username, password, code } = req.body;

      const result = await services.otpVerification({
        code: code,
        type: "register",
        username: username,
        password: password,
        reply: reply,
      });

      return customResponse(reply, result, 200, "OTP Verification Success");
    } catch (error) {
      return customError(error, reply);
    }
  },
};
export default controllers;
