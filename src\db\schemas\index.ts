import { relations } from "drizzle-orm";
import {
  boolean,
  integer,
  jsonb,
  pgEnum,
  pgTable,
  primaryKey,
  serial,
  text,
  timestamp,
  varchar,
} from "drizzle-orm/pg-core";

export const statusEnum = pgEnum("status_enum", ["success", "failed"]);

export const typeEnum = pgEnum("type_enum", ["api_call"]);

export const typePointEnum = pgEnum("type_point_enum", ["standard", "bonus"]);

export const counts = pgTable(
  "counts",
  {
    id: serial("id").unique(),
    status: statusEnum("status_enum"),
    total: integer("total"),
    type: typeEnum("type_enum").default("api_call"),
    created_at: timestamp("created_at").defaultNow(),
  },
  (table) => [primaryKey({ columns: [table.id] })]
);

export const users = pgTable("users", {
  id: serial("id").unique(),
  hubspot_id: text("hubspot_id"),
  username: text("username"),
  password: text("password"),
  point: integer("point"),
  level: integer("level"),
  latest_usage_bonus_level: timestamp("latest_usage_bonus_level"),
  is_active: boolean("is_active").default(true),
  created_at: timestamp("created_at").defaultNow(),
  updated_at: timestamp("updated_at").defaultNow(),
});

export const points = pgTable("points", {
  id: serial("id").unique(),
  user_id: integer("user_id"),
  from_point: integer("from_point"),
  to_point: integer("to_point"),
  default_point: integer("default_point"),
  type_point: typePointEnum("type_point_enum"),
  created_at: timestamp("created_at").defaultNow(),
  updated_at: timestamp("updated_at").defaultNow(),
});

export const pointsRelations = relations(points, ({ one }) => ({
  user: one(users, {
    fields: [points.user_id],
    references: [users.id],
  }),
}));

export const usersRelations = relations(users, ({ many }) => ({
  points: many(points),
}));

export const otp = pgTable("otp", {
  id: serial("id").unique(),
  user_id: integer("user_id"),
  code: text("code"),
  expired_at: timestamp("expired_at").notNull(),
});

export const otpRelations = relations(otp, ({ one }) => ({
  user: one(users, {
    fields: [otp.user_id],
    references: [users.id],
  }),
}));

export type typeEnumType = typeof typeEnum;
