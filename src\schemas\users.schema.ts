import { z } from "zod";

export const usersSchemas = z.object({
  username: z.string().min(2).max(255),
  password: z.string().min(6).max(255),
});

export type UsersSchemasType = z.infer<typeof usersSchemas>;

// export const usersLoginSchemas = z.object({
//   body: usersSchemas,
// });

// export const usersRegisterSchemas = z.object({
//   body: usersSchemas,
// });

export const usersRegisterSchema = z.object({
  username: z.string().min(2).max(255),
  password: z.string().min(6).max(255),
});
export type UsersRegisterSchemasType = z.infer<typeof usersRegisterSchema>;
export const usersRegisterSchemas = z.object({
  body: usersRegisterSchema,
});

export const usersLoginSchema = z.object({
  username: z.string().min(2).max(255),
  password: z.string().min(6).max(255),
});
export type UsersLoginSchemaType = z.infer<typeof usersLoginSchema>;
export const usersLoginSchemas = z.object({
  body: usersLoginSchema,
});
